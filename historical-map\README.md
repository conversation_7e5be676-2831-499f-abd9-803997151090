# Historical World Map

An interactive world map application built with React, D3.js, and Vite that displays historical geographical data with intelligent label management and zoom controls.

## Features

- **Interactive World Map**: Built with D3.js using Natural Earth projection
- **Smart Label System**: Progressive country name display based on zoom level and country size
- **Traditional Cartographic Approach**: Follows map design best practices for label visibility and font sizing
- **Zoom Controls**: Mouse wheel zoom, zoom buttons, and reset functionality
- **Responsive Design**: Optimized for different screen sizes and zoom levels
- **Country Categorization**: Intelligent sizing system (large, medium, small, tiny) with appropriate labels

## Label Management System

The application implements a sophisticated label management system that follows traditional cartographic principles:

### Size-Based Categorization
- **Large Countries** (>2000px²): Full names, appear at base zoom (1.0x)
- **Medium Countries** (500-2000px²): Truncated names, appear at 1.5x zoom
- **Small Countries** (100-500px²): Abbreviations, appear at 2.5x zoom
- **Tiny Countries** (<100px²): Country codes, appear at 4.0x zoom

### Font Size Management
- Font sizes automatically adjust with zoom level to prevent overlapping
- Uses inverse scaling to maintain readability at high zoom levels
- Fixed minimum font sizes to ensure legibility

## Technology Stack

- **React 19.1.0**: Modern React with hooks for state management
- **D3.js 7.9.0**: Data visualization and SVG manipulation
- **Vite 7.0.4**: Fast build tool and development server
- **ESLint**: Code linting and formatting

## Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd historical-map
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev`: Start development server with hot reload
- `npm run build`: Build for production
- `npm run preview`: Preview production build locally
- `npm run lint`: Run ESLint for code quality checks

## Project Structure

```
historical-map/
├── src/
│   ├── components/
│   │   ├── HistoricalMap.jsx    # Main map component
│   │   └── HistoricalMap.css    # Map styling
│   ├── App.jsx                  # Root component
│   ├── App.css                  # Global styles
│   └── main.jsx                 # Application entry point
├── public/                      # Static assets
├── package.json                 # Dependencies and scripts
└── vite.config.js              # Vite configuration
```

## Data Source

The application loads GeoJSON data from the `../historical-basemaps/geojson/` directory. Currently configured to display the world map for the year 2000 (`world_2000.geojson`).

## Map Controls

- **Mouse Wheel**: Zoom in/out
- **Zoom Buttons**:
  - `+`: Zoom in (1.5x multiplier)
  - `−`: Zoom out (0.67x multiplier)
  - `⌂`: Reset to default view
- **Pan**: Click and drag to move around the map

## Customization

### Changing the Year
To display a different year, modify the `currentYear` variable in `HistoricalMap.jsx`:
```javascript
const currentYear = 2000 // Change to desired year
```

### Adjusting Label Thresholds
Modify the `thresholds` object in the `updateLabelVisibility` function to change when labels appear:
```javascript
const thresholds = {
  large: 1.0,    // Large countries appear at base zoom
  medium: 1.5,   // Medium countries at slight zoom
  small: 2.5,    // Small countries at moderate zoom
  tiny: 4.0      // Tiny countries only at higher zoom
}
```

### Color Scheme
Update the color scale in the map rendering section to change country colors:
```javascript
const colorScale = d3.scaleOrdinal()
  .range([
    "#FF6B6B", "#4ECDC4", "#45B7D1", // Add your colors here
    // ... more colors
  ])
```

## Browser Support

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Historical basemap data from the `historical-basemaps` project
- D3.js community for excellent documentation and examples
- Natural Earth for geographical data standards
