import { useState, useEffect, useRef } from 'react'
import * as d3 from 'd3'
import './HistoricalMap.css'

// Helper function to get country abbreviations
const getCountryAbbreviation = (name) => {
  const abbreviations = {
    'United States': 'USA',
    'United Kingdom': 'UK',
    'Russian Federation': 'Russia',
    'People\'s Republic of China': 'China',
    'Federal Republic of Germany': 'Germany',
    'French Republic': 'France',
    'Kingdom of Spain': 'Spain',
    'Italian Republic': 'Italy',
    'Republic of India': 'India',
    'Federative Republic of Brazil': 'Brazil',
    'Commonwealth of Australia': 'Australia',
    'Dominion of Canada': 'Canada',
    'United Mexican States': 'Mexico',
    'Republic of South Africa': 'S.Africa',
    'Argentine Republic': 'Argentina',
    'Oriental Republic of Uruguay': 'Uruguay',
    'Republic of Chile': 'Chile',
    'Republic of Peru': 'Peru',
    'Republic of Colombia': 'Colombia',
    'Bolivarian Republic of Venezuela': 'Venezuela',
    'Republic of Ecuador': 'Ecuador',
    'Plurinational State of Bolivia': 'Bolivia',
    'Republic of Paraguay': 'Paraguay',
    'Co-operative Republic of Guyana': 'Guyana',
    'Republic of Suriname': 'Suriname'
  }

  return abbreviations[name] || (name.length > 6 ? name.substring(0, 6) + "." : name)
}

// Helper function to get country codes for very small countries
const getCountryCode = (name) => {
  const codes = {
    'United States': 'US',
    'United Kingdom': 'GB',
    'Russian Federation': 'RU',
    'People\'s Republic of China': 'CN',
    'Federal Republic of Germany': 'DE',
    'French Republic': 'FR',
    'Kingdom of Spain': 'ES',
    'Italian Republic': 'IT',
    'Republic of India': 'IN',
    'Federative Republic of Brazil': 'BR',
    'Commonwealth of Australia': 'AU',
    'Dominion of Canada': 'CA',
    'United Mexican States': 'MX',
    'Republic of South Africa': 'ZA',
    'Argentine Republic': 'AR',
    'Oriental Republic of Uruguay': 'UY',
    'Republic of Chile': 'CL',
    'Republic of Peru': 'PE',
    'Republic of Colombia': 'CO',
    'Bolivarian Republic of Venezuela': 'VE',
    'Republic of Ecuador': 'EC',
    'Plurinational State of Bolivia': 'BO',
    'Republic of Paraguay': 'PY',
    'Co-operative Republic of Guyana': 'GY',
    'Republic of Suriname': 'SR',
    'Vatican City': 'VA',
    'San Marino': 'SM',
    'Monaco': 'MC',
    'Liechtenstein': 'LI',
    'Andorra': 'AD',
    'Malta': 'MT',
    'Luxembourg': 'LU'
  }

  return codes[name] || name.substring(0, 2).toUpperCase()
}

// Traditional map label management following cartographic best practices
const updateLabelVisibility = (g, scale, path, transform) => {
  // Define zoom thresholds for different label categories (traditional map approach)
  const thresholds = {
    large: 1.0,    // Large countries appear at base zoom
    medium: 1.5,   // Medium countries at slight zoom
    small: 2.5,    // Small countries at moderate zoom
    tiny: 4.0      // Tiny countries only at higher zoom
  }

  g.selectAll(".country-label")
    .style("opacity", function() {
      const data = d3.select(this).datum()
      const threshold = thresholds[data.sizeCategory]

      if (scale < threshold) return 0

      // Traditional map approach: Quick fade-in, then stable visibility
      let opacity = Math.min(1, (scale - threshold) / 0.5 + 0.3)

      // Traditional maps keep labels visible at high zoom but may reduce density
      if (scale > 6) {
        // Only reduce opacity for tiny labels at very high zoom to prevent clutter
        if (data.sizeCategory === 'tiny') opacity *= 0.7
      }

      return opacity
    })
    .style("font-size", function() {
      const data = d3.select(this).datum()

      
      const scaleFactor = Math.max(0.3, 1 / Math.sqrt(scale))

      // Apply inverse scaling to base font sizes
      const adjustedSize = Math.max(2, data.baseFontSize * scaleFactor)

      return `${adjustedSize}px`
    })
}

const HistoricalMap = () => {
  const currentYear = 2000 // Fixed to current/modern map
  const [mapData, setMapData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [currentZoom, setCurrentZoom] = useState(1)
  const svgRef = useRef()

  // Load map data for current year
  useEffect(() => {
    const loadMapData = async () => {
      setLoading(true)
      try {
        const filename = `world_${currentYear}.geojson`
        const response = await fetch(`../historical-basemaps/geojson/${filename}`)
        if (!response.ok) {
          throw new Error(`Failed to load ${filename}`)
        }
        const data = await response.json()
        setMapData(data)
      } catch (error) {
        console.error('Error loading map data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMapData()
  }, [])

  // Render map using D3
  useEffect(() => {
    if (!mapData || !svgRef.current) return

    const svg = d3.select(svgRef.current)
    svg.selectAll("*").remove() // Clear previous render

    const width = 1200
    const height = 600

    // Set up projection
    const projection = d3.geoNaturalEarth1()
      .scale(180)
      .translate([width / 2, height / 2])

    const path = d3.geoPath().projection(projection)

    // Create main group
    const g = svg.append("g")

    // Color scale for different countries/regions
    const colorScale = d3.scaleOrdinal()
      .domain(mapData.features.map((d, i) => i))
      .range([
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
        "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
        "#A3E4D7", "#F9E79F", "#D5A6BD", "#AED6F1", "#A9DFBF",
        "#FAD7A0", "#D2B4DE", "#AED6F1", "#A3E4D7", "#F7DC6F"
      ])

    // Add countries
    g.selectAll(".country")
      .data(mapData.features)
      .enter().append("path")
      .attr("class", "country")
      .attr("d", path)
      .attr("fill", (d, i) => colorScale(i))
      .attr("stroke", "rgba(255, 255, 255, 0.8)")
      .attr("stroke-width", 0.8)
      .style("opacity", 0.85)
      .on("mouseover", function(event, d) {
        d3.select(this).style("opacity", 1)
      })
      .on("mouseout", function() {
        d3.select(this).style("opacity", 0.85)
      })

    // Calculate country sizes and categorize them
    const countriesWithSizes = mapData.features
      .filter(d => d.properties && d.properties.NAME)
      .map(d => {
        const bounds = path.bounds(d)
        const width = bounds[1][0] - bounds[0][0]
        const height = bounds[1][1] - bounds[0][1]
        const area = width * height

        // Categorize countries by size with traditional map font sizing
        let sizeCategory, fontSize, baseFontSize, minZoom, textContent
        if (area > 2000) {
          sizeCategory = 'large'
          fontSize = 7
          baseFontSize = 7  // Base font size for zoom <= 1.5
          minZoom = 1.0
          textContent = d.properties.NAME.length > 12 ? d.properties.NAME.substring(0, 12) + "..." : d.properties.NAME
        } else if (area > 500) {
          sizeCategory = 'medium'
          fontSize = 5
          baseFontSize = 5
          minZoom = 1.5
          textContent = d.properties.NAME.length > 8 ? d.properties.NAME.substring(0, 8) + "..." : d.properties.NAME
        } else if (area > 100) {
          sizeCategory = 'small'
          fontSize = 4
          baseFontSize = 4
          minZoom = 2.5
          // Use abbreviations or short names for small countries
          textContent = getCountryAbbreviation(d.properties.NAME)
        } else {
          sizeCategory = 'tiny'
          fontSize = 3
          baseFontSize = 3
          minZoom = 4.0
          // Use country codes for tiny countries
          textContent = getCountryCode(d.properties.NAME)
        }

        return { ...d, area, sizeCategory, fontSize, baseFontSize, minZoom, textContent }
      })

    // Add country labels with size-based styling
    g.selectAll(".country-label")
      .data(countriesWithSizes)
      .enter().append("text")
      .attr("class", d => `country-label ${d.sizeCategory}`)
      .attr("transform", function(d) {
        const centroid = path.centroid(d)
        return `translate(${centroid[0]}, ${centroid[1]})`
      })
      .attr("text-anchor", "middle")
      .attr("dy", "0.35em")
      .style("font-size", d => `${d.baseFontSize}px`)
      .style("font-weight", d => d.sizeCategory === 'large' ? "600" : "500")
      .style("fill", "#2d3748")
      .style("stroke", "white")
      .style("stroke-width", d => d.sizeCategory === 'large' ? "1.5px" : "1px")
      .style("paint-order", "stroke")
      .style("pointer-events", "none")
      .style("opacity", 0) // Initially hidden
      .text(d => d.textContent)

    // Add zoom behavior with best practices
    const zoom = d3.zoom()
      .scaleExtent([0.8, 12]) // Wider zoom range for better exploration
      .on("zoom", (event) => {
        const scale = event.transform.k
        setCurrentZoom(scale)

        // Apply transform to map
        g.attr("transform", event.transform)

        // Best practice: Progressive label display with smart density management
        updateLabelVisibility(g, scale, path, event.transform)

        // Best practice: Adjust stroke width based on zoom for better visibility
        g.selectAll(".country")
          .attr("stroke-width", Math.max(0.3, 0.8 / scale))
      })

    svg.call(zoom)

    // Best practice: Add zoom controls
    const zoomControls = svg.append("g")
      .attr("class", "zoom-controls")
      .attr("transform", "translate(20, 20)")

    // Zoom in button
    zoomControls.append("rect")
      .attr("class", "zoom-button")
      .attr("width", 30)
      .attr("height", 30)
      .attr("fill", "rgba(255, 255, 255, 0.9)")
      .attr("stroke", "#ccc")
      .attr("rx", 4)
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(300).call(zoom.scaleBy, 1.5)
      })

    zoomControls.append("text")
      .attr("x", 15)
      .attr("y", 20)
      .attr("text-anchor", "middle")
      .attr("font-size", "16px")
      .attr("font-weight", "bold")
      .attr("fill", "#333")
      .style("pointer-events", "none")
      .text("+")

    // Zoom out button
    zoomControls.append("rect")
      .attr("class", "zoom-button")
      .attr("y", 35)
      .attr("width", 30)
      .attr("height", 30)
      .attr("fill", "rgba(255, 255, 255, 0.9)")
      .attr("stroke", "#ccc")
      .attr("rx", 4)
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(300).call(zoom.scaleBy, 0.67)
      })

    zoomControls.append("text")
      .attr("x", 15)
      .attr("y", 55)
      .attr("text-anchor", "middle")
      .attr("font-size", "16px")
      .attr("font-weight", "bold")
      .attr("fill", "#333")
      .style("pointer-events", "none")
      .text("−")

    // Reset zoom button
    zoomControls.append("rect")
      .attr("class", "zoom-button")
      .attr("y", 70)
      .attr("width", 30)
      .attr("height", 30)
      .attr("fill", "rgba(255, 255, 255, 0.9)")
      .attr("stroke", "#ccc")
      .attr("rx", 4)
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(500).call(zoom.transform, d3.zoomIdentity)
      })

    zoomControls.append("text")
      .attr("x", 15)
      .attr("y", 90)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("font-weight", "bold")
      .attr("fill", "#333")
      .style("pointer-events", "none")
      .text("⌂")

    svg.call(zoom)

  }, [mapData])

  return (
    <div className="historical-map">
      <div className="controls">
        <div className="current-year">
          <h2>World Map - 2000 AD</h2>
          <p className="zoom-hint">Use zoom controls or mouse wheel • Country names appear progressively</p>
          <div className="zoom-info">
            <span className="zoom-level">Zoom: {currentZoom.toFixed(1)}x</span>
          </div>
        </div>
      </div>
      
      <div className="map-container">
        {loading && <div className="loading">Loading map data...</div>}
        <svg
          ref={svgRef}
          width="1200"
          height="600"
          style={{ border: '1px solid #e2e8f0', background: '#f8f9fa', borderRadius: '8px' }}
        />
      </div>
    </div>
  )
}

export default HistoricalMap
